package com.renpho.erp.smc.infrastructure.persistence.po.transformer;

import java.util.ArrayList;
import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.MapperConfig;
import org.mapstruct.factory.Mappers;

import com.renpho.erp.smc.domain.organizationmanagement.office.OfficeLocationAssign;
import com.renpho.erp.smc.infrastructure.persistence.po.OumOfficeLocationAssignPO;

@Mapper
@MapperConfig
public interface OfficeLocationAssignTransformer {

	/**
	 * 实例
	 */
	OfficeLocationAssignTransformer INSTANCE = Mappers.getMapper(OfficeLocationAssignTransformer.class);

	default Integer mapOfficeLocationAssignID(OfficeLocationAssign.OfficeLocationAssignID value) {
		if (null == value) {
			return null;
		}
		return value.getId();
	}

	default List<OumOfficeLocationAssignPO> toOfficeLocationAssignPOList(List<OfficeLocationAssign> officeLocationAssignList) {

		if (officeLocationAssignList == null) {
			return null;
		}
		List<OumOfficeLocationAssignPO> poList = new ArrayList<>();
		for (OfficeLocationAssign officeLocationAssign : officeLocationAssignList) {
			poList.add(this.toOfficeLocationAssignPO(officeLocationAssign));
		}
		return poList;
	}

	default OumOfficeLocationAssignPO toOfficeLocationAssignPO(OfficeLocationAssign officeLocationAssign) {
		OumOfficeLocationAssignPO officeLocationAssignPO = new OumOfficeLocationAssignPO();
		officeLocationAssignPO.setId(this.mapOfficeLocationAssignID(officeLocationAssign.getId()));
		officeLocationAssignPO.setUserId(officeLocationAssign.getUserId());
		officeLocationAssignPO.setOfficeLocationId(officeLocationAssign.getOfficeLocationId());
		officeLocationAssignPO.setEffectiveDate(officeLocationAssign.getEffectiveDate());
		return officeLocationAssignPO;
	}

	default OfficeLocationAssign toOfficeLocationAssign(OumOfficeLocationAssignPO officeLocationAssignPO) {
		return new OfficeLocationAssign(OfficeLocationAssign.OfficeLocationAssignID.of(officeLocationAssignPO.getId()),
				officeLocationAssignPO.getUserId(), officeLocationAssignPO.getOfficeLocationId(),
				officeLocationAssignPO.getEffectiveDate());
	}

}
