package com.renpho.erp.tms.adapter.web.controller.transferorder.vo;

import com.fhs.core.trans.vo.VO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 调拨单商品 VO
 *
 * <AUTHOR>
 * @since 2025/8/23
 */
@Getter
@Setter
public class TransferOrderItemVO implements Serializable, VO {
    @Serial
    private static final long serialVersionUID = 4696821097346251943L;

    private Integer id;

    /**
     * 调拨单ID
     */
    private Integer tsId;

    /**
     * 调拨单号
     */
    private String tsNo;

    /**
     * PSKU
     */
    private String psku;

    /**
     * FNSKU
     */
    private String fnsku;

    /**
     * 销售渠道ID
     */
    private Integer salesChannelId;

    /**
     * 销售渠道编码
     */
    private String salesChannelCode;

    /**
     * 销售渠道名称
     */
    private String salesChannelName;

    /**
     * 店铺ID
     */
    private Integer storeId;

    /**
     * 店铺编码
     */
    private String storeCode;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 货主ID
     */
    private Integer ownerId;

    /**
     * 货主编码
     */
    private String ownerCode;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 出库单号
     */
    private String outboundNo;

    /**
     * 发货数量
     */
    private Integer qty;

    /**
     * 是否借货
     */
    private Boolean isBorrowed;

    /**
     * 是否换标
     */
    private Boolean isRelabel;

    /**
     * 是否换标完成
     */
    private Boolean isRelabelFinish;

    /**
     * 箱数
     */
    private BigDecimal boxQty;

    /**
     * 总毛重
     */
    private BigDecimal totalGrossWeight;

    /**
     * 总体积
     */
    private BigDecimal totalVolume;

    /**
     * 换标完成时间
     */
    private LocalDateTime relabelFinishTime;

    /**
     * 销售金额
     */
    private BigDecimal saleAmount;

    /**
     * 箱唛文件ID
     */
    private List<String> cartonLabelFileIds;

    /**
     * ASN标签文件ID
     */
    private List<String> asnLabelFileIds;

    /**
     * 出借方PSKU
     */
    private String borrowedPsku;

    /**
     * 出借方FNSKU
     */
    private String borrowedFnsku;

    /**
     * 新产品标签文件ID
     */
    private List<String> newProductLabelFileIds;

    /**
     * 条码文件ID
     */
    private List<String> barcodeFileIds;

    /**
     * 出借方店铺ID
     */
    private Integer borrowedStoreId;

    /**
     * 出借方店铺名称
     */
    private String borrowedStoreName;

    /**
     * 出借方货主ID
     */
    private Integer borrowedOwnerId;

    /**
     * 出借方货主编码
     */
    private String borrowedOwnerCode;

    /**
     * 出借方货主名称
     */
    private String borrowedOwnerName;

    /**
     * 签收数量
     */
    private Integer receivedQty;

    /**
     * 签收差异
     */
    private Integer receivedDiscrepancy;

    /**
     * 签收开始时间
     */
    private LocalDateTime receivedStartTime;

    /**
     * 签收结束时间
     */
    private LocalDateTime receivedEndTime;

    /**
     * 上架数量
     */
    private Integer putawayQty;

    /**
     * 上架差异
     */
    private Integer putawayDiscrepancy;

    /**
     * 上架开始时间
     */
    private LocalDateTime putawayStartTime;

    /**
     * 上架结束时间
     */
    private LocalDateTime putawayEndTime;

    /**
     * 尺寸单位
     */
    private String dimensionUnit;

    /**
     * 外箱尺寸-长
     */
    private BigDecimal boxLength;

    /**
     * 外箱尺寸-宽
     */
    private BigDecimal boxWidth;

    /**
     * 外箱尺寸-高
     */
    private BigDecimal boxHeight;

    /**
     * 重量单位
     */
    private String weightUnit;

    /**
     * 单品净重
     */
    private BigDecimal weight;

    /**
     * 单品毛重
     */
    private BigDecimal grossWeight;

    /**
     * 整箱毛重
     */
    private BigDecimal boxGrossWeight;

    /**
     * 装箱数量
     */
    private Integer quantityPerBox;

    /**
     * 创建人ID
     */
    private Integer createBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 创建人工号
     */
    private String createByCode;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private Integer updateBy;

    /**
     * 更新人名称
     */
    private String updateByName;

    /**
     * 更新人工号
     */
    private String updateByCode;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
