package com.renpho.erp.tms.infrastructure.persistence.transferorder.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.renpho.erp.tms.domain.transferorder.TransferOrderDataSource;
import com.renpho.erp.tms.domain.transportrequest.SyncApiStatus;
import com.renpho.karma.cloud.mybatisplus.po.DefaultPO;
import lombok.Getter;
import lombok.Setter;
import org.apache.ibatis.type.EnumTypeHandler;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 调拨单主表 PO
 *
 * <AUTHOR>
 * @since 2025/8/22
 */
@Getter
@Setter
@TableName(value = "tms_transfer_order", autoResultMap = true)
public class TransferOrderPO extends DefaultPO {

    @Serial
    private static final long serialVersionUID = 569914950338519619L;

    /**
     * 调拨单单号
     */
    @TableField(value = "ts_no")
    private String tsNo;

    /**
     * OMS的单号
     */
    @TableField(value = "order_no")
    private String orderNo;

    /**
     * OMS的订单参考号
     */
    @TableField(value = "ref_no")
    private String refNo;

    /**
     * 调拨单类型, 字典: transfer_order_type
     */
    @TableField(value = "type")
    private String type;

    /**
     * 业务类型, 字典: transfer_order_biz_type
     */
    @TableField(value = "biz_type")
    private String bizType;

    /**
     * 差异单号
     */
    @TableField(value = "do_no")
    private String doNo;
    /**
     * 差异单状态
     */
    @TableField(value = "do_status")
    private String doStatus;

    /**
     * 数据来源, 判断是TMS创建还是上游传入
     */
    @TableField(value = "data_source", typeHandler = EnumTypeHandler.class)
    private TransferOrderDataSource dataSource;

    /**
     * 销售渠道ID
     */
    @TableField(value = "sales_channel_id")
    private Integer salesChannelId;

    /**
     * 店铺ID
     */
    @TableField(value = "store_id")
    private Integer storeId;

    /**
     * 货主ID
     */
    @TableField(value = "owner_id")
    private Integer ownerId;

    /**
     * 发货仓
     */
    @TableField(value = "shipping_warehouse_id")
    private Integer shippingWarehouseId;

    /**
     * 发货仓Code
     */
    @TableField(value = "shipping_warehouse_code")
    private String shippingWarehouseCode;

    /**
     * 目的国/地区
     */
    @TableField(value = "dest_country_code")
    private String destCountryCode;

    /**
     * 目的仓库ID
     */
    @TableField(value = "dest_warehouse_id")
    private Integer destWarehouseId;

    /**
     * 目的仓Code
     */
    @TableField(value = "dest_warehouse_code")
    private String destWarehouseCode;

    /**
     * 目的地
     */
    @TableField(value = "dest_address")
    private String destAddress;

    /**
     * 贸易条款, 字典: trade_terms
     */
    @TableField(value = "trade_terms")
    private String tradeTerms;

    /**
     * 付款条款, 字典: payment_terms
     */
    @TableField(value = "payment_terms")
    private String paymentTerms;

    /**
     * 是否打托
     */
    @TableField(value = "is_palletized")
    private Boolean isPalletized;

    /**
     * 预估发货时间
     */
    @TableField(value = "estimated_departure_time")
    private LocalDateTime estimatedDepartureTime;

    /**
     * 预估交货时间
     */
    @TableField(value = "estimated_delivery_time")
    private LocalDateTime estimatedDeliveryTime;

    /**
     * 实际发货时间
     */
    @TableField(value = "actual_departure_time")
    private LocalDateTime actualDepartureTime;

    /**
     * 实际交货时间
     */
    @TableField(value = "actual_delivery_time")
    private LocalDateTime actualDeliveryTime;

    /**
     * 期望上架时间
     */
    @TableField(value = "expected_putaway_time")
    private LocalDateTime expectedPutawayTime;

    /**
     * 总数量
     */
    @TableField(value = "qty")
    private Integer qty;

    /**
     * 总箱数
     */
    @TableField(value = "box_qty")
    private BigDecimal boxQty;

    /**
     * 总毛重
     */
    @TableField(value = "gross_weight")
    private BigDecimal grossWeight;

    /**
     * 总净重
     */
    @TableField(value = "net_weight")
    private BigDecimal netWeight;

    /**
     * 总体积
     */
    @TableField(value = "volume")
    private BigDecimal volume;

    /**
     * 提单类型, 字典: bill_of_lading_type
     */
    @TableField(value = "bill_of_lading_type")
    private String billOfLadingType;

    /**
     * 发票号
     */
    @TableField(value = "invoice_no")
    private String invoiceNo;

    /**
     * 允许发货, 0-不允许, 1-允许
     */
    @TableField(value = "is_allowed_shipping")
    private Boolean isAllowedShipping;

    /**
     * 关闭时间
     */
    @TableField(value = "closed_time")
    private LocalDateTime closedTime;

    /**
     * 运营人员ID
     */
    @TableField(value = "sales_staff_id")
    private Integer salesStaffId;

    /**
     * 计划人员ID
     */
    @TableField(value = "planer_staff_id")
    private Integer planerStaffId;

    /**
     * 船务人员ID
     */
    @TableField(value = "shipping_staff_id")
    private Integer shippingStaffId;

    /**
     * 发货Id
     */
    @TableField(value = "shipment_id")
    private String shipmentId;

    /**
     * 箱唛文件ID
     */
    @TableField(value = "carton_label_file_id", typeHandler = FastjsonTypeHandler.class)
    private List<String> cartonLabelFileIds;

    /**
     * 同步API状态
     */
    @TableField(value = "sync_api_status", typeHandler = EnumTypeHandler.class)
    private SyncApiStatus syncApiStatus;
}
